-- Add default workflow for lead answer status changes
-- This migration creates a default workflow that automatically sends WhatsApp messages
-- when lead call status is updated (e.g., "לא ענה 1", "לא ענה 2", etc.)

-- First, add support for lead_answer_status_change trigger type in workflows
ALTER TABLE public.workflows 
DROP CONSTRAINT IF EXISTS workflows_trigger_type_check;

ALTER TABLE public.workflows 
ADD CONSTRAINT workflows_trigger_type_check 
CHECK (trigger_type IN ('lead_status_change', 'case_status_change', 'manual', 'lead_answer_status_change'));

-- Function to create default lead answer status workflow for each company
CREATE OR REPLACE FUNCTION create_default_lead_answer_workflow()
RETURNS void AS $$
DECLARE
    company_record RECORD;
    workflow_id UUID;
BEGIN
    -- Loop through all companies and create the default workflow
    FOR company_record IN SELECT id FROM public.companies LOOP
        -- Check if workflow already exists for this company
        IF NOT EXISTS (
            SELECT 1 FROM public.workflows 
            WHERE company_id = company_record.id 
            AND trigger_type = 'lead_answer_status_change'
            AND name = 'ת<PERSON><PERSON><PERSON><PERSON> אוטומטית לסטטוס מענה'
        ) THEN
            -- Create the workflow
            INSERT INTO public.workflows (
                company_id,
                name,
                description,
                trigger_type,
                trigger_config,
                is_active,
                created_by
            ) VALUES (
                company_record.id,
                'תגובה אוטומטית לסטטוס מענה',
                'שולח הודעת וואטסאפ אוטומטית כאשר מתעדכן סטטוס המענה של ליד',
                'lead_answer_status_change',
                jsonb_build_object(
                    'from_status', 'any',
                    'to_status', 'לא ענה*'
                ),
                true,
                (SELECT id FROM auth.users LIMIT 1) -- Use first available user as creator
            ) RETURNING id INTO workflow_id;

            -- Create the WhatsApp step
            INSERT INTO public.workflow_steps (
                workflow_id,
                step_order,
                step_type,
                step_config
            ) VALUES (
                workflow_id,
                1,
                'send_whatsapp',
                jsonb_build_object(
                    'recipient', 'lead',
                    'message', 'שלום {lead_name}, זה {company_name}, ניסינו להשיג אותך אך לא הצלחנו, מתי יהיה זמן טוב יותר להתקשר אליך?'
                )
            );

            RAISE NOTICE 'Created default lead answer workflow for company %', company_record.id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Execute the function to create workflows for existing companies
SELECT create_default_lead_answer_workflow();

-- Create trigger to automatically create the workflow for new companies
CREATE OR REPLACE FUNCTION create_default_workflows_for_new_company()
RETURNS TRIGGER AS $$
DECLARE
    workflow_id UUID;
BEGIN
    -- Create default lead answer status workflow
    INSERT INTO public.workflows (
        company_id,
        name,
        description,
        trigger_type,
        trigger_config,
        is_active,
        created_by
    ) VALUES (
        NEW.id,
        'תגובה אוטומטית לסטטוס מענה',
        'שולח הודעת וואטסאפ אוטומטית כאשר מתעדכן סטטוס המענה של ליד',
        'lead_answer_status_change',
        jsonb_build_object(
            'from_status', 'any',
            'to_status', 'לא ענה*'
        ),
        true,
        NEW.created_by
    ) RETURNING id INTO workflow_id;

    -- Create the WhatsApp step
    INSERT INTO public.workflow_steps (
        workflow_id,
        step_order,
        step_type,
        step_config
    ) VALUES (
        workflow_id,
        1,
        'send_whatsapp',
        jsonb_build_object(
            'recipient', 'lead',
            'message', 'שלום {lead_name}, זה {company_name}, ניסינו להשיג אותך אך לא הצלחנו, מתי יהיה זמן טוב יותר להתקשר אליך?'
        )
    );

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new companies
DROP TRIGGER IF EXISTS create_default_workflows_trigger ON public.companies;
CREATE TRIGGER create_default_workflows_trigger
    AFTER INSERT ON public.companies
    FOR EACH ROW
    EXECUTE FUNCTION create_default_workflows_for_new_company();

-- Clean up the function (no longer needed after execution)
DROP FUNCTION IF EXISTS create_default_lead_answer_workflow();
