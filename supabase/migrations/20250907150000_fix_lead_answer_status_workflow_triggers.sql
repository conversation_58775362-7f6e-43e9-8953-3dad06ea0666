-- Fix lead answer status workflow triggers
-- This migration adds proper database triggers for lead_answer_status changes
-- to ensure consistency with the workflow_triggers system

-- Function to create workflow trigger for lead answer status changes
CREATE OR REPLACE FUNCTION public.create_lead_answer_status_workflow_trigger()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle both INSERT and UPDATE operations
  IF TG_OP = 'INSERT' THEN
    -- For INSERT, there's no old status
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.lead_id,
      NULL, -- No old status for INSERT
      NEW.answer_status,
      NEW.company_id
    );
    RETURN NEW;
  ELSIF TG_OP = 'UPDATE' AND OLD.answer_status IS DISTINCT FROM NEW.answer_status THEN
    -- For UPDATE, only create trigger if answer_status actually changed
    INSERT INTO public.workflow_triggers (
      entity_type,
      entity_id,
      old_status,
      new_status,
      company_id
    ) VALUES (
      'lead',
      NEW.lead_id,
      OLD.answer_status,
      NEW.answer_status,
      NEW.company_id
    );
    RETURN NEW;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER
SET search_path = public;

-- Create trigger on lead_answer_status table
DROP TRIGGER IF EXISTS lead_answer_status_workflow_trigger ON public.lead_answer_status;
CREATE TRIGGER lead_answer_status_workflow_trigger
  AFTER INSERT OR UPDATE ON public.lead_answer_status
  FOR EACH ROW
  EXECUTE FUNCTION public.create_lead_answer_status_workflow_trigger();

-- Update workflow_triggers entity_type constraint to support lead answer status
-- First check if the constraint exists and what values it currently allows
DO $$
BEGIN
  -- Drop the existing constraint if it exists
  IF EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'workflow_triggers_entity_type_check' 
    AND table_name = 'workflow_triggers'
  ) THEN
    ALTER TABLE public.workflow_triggers DROP CONSTRAINT workflow_triggers_entity_type_check;
  END IF;
  
  -- Add the updated constraint that includes lead answer status
  ALTER TABLE public.workflow_triggers 
  ADD CONSTRAINT workflow_triggers_entity_type_check 
  CHECK (entity_type IN ('lead', 'case'));
END $$;

-- Add comment for documentation
COMMENT ON FUNCTION public.create_lead_answer_status_workflow_trigger() IS 'Creates workflow triggers when lead answer status changes (לא ענה 1, לא ענה 2, etc.)';

-- Note: The workflow executor already handles lead_answer_status_change trigger type
-- and uses pattern matching for "לא ענה*" so no changes needed there
